const jwt = require('jsonwebtoken');
const User = require('../models/User');

const protect = async (req, res, next) => {
    try {
        let token;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        if (!token) {
            return res.status(401).json({
                status: 'fail',
                message: 'You are not logged in. Please log in to get access.'
            });
        }

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            const currentUser = await User.findById(decoded.userId);
            if (!currentUser) {
                return res.status(401).json({
                    status: 'fail',
                    message: 'The user belonging to this token no longer exists.'
                });
            }

            req.user = currentUser;
            next();
        } catch (error) {
            return res.status(401).json({
                status: 'fail',
                message: 'Invalid token or token expired'
            });
        }
    } catch (error) {
        return res.status(500).json({
            status: 'error',
            message: 'Internal server error'
        });
    }
};

// Middleware to check if user is admin (DISABLED - allows all authenticated users)
const requireAdmin = async (req, res, next) => {
    try {
        // First ensure user is authenticated
        if (!req.user) {
            return res.status(401).json({
                status: 'fail',
                message: 'You must be logged in to access this resource.'
            });
        }

        // ADMIN CHECK DISABLED - Allow all authenticated users
        // if (!req.user.isAdmin) {
        //     return res.status(403).json({
        //         status: 'fail',
        //         message: 'Access denied. Admin privileges required.'
        //     });
        // }

        next();
    } catch (error) {
        return res.status(500).json({
            status: 'error',
            message: 'Internal server error'
        });
    }
};

// Combined middleware for admin protection (auth + admin check)
const protectAdmin = [protect, requireAdmin];

const validateRegistration = (req, res, next) => {
    const { name, email, password, phone } = req.body;

    if (!name || !email || !password || !phone) {
        return res.status(400).json({
            status: 'fail',
            message: 'Please provide all required fields: name, email, password, phone'
        });
    }

    if (!/^[A-Za-z\s]{2,25}$/.test(name)) {
        return res.status(400).json({
            status: 'fail',
            message: 'Name must be 2-25 characters long and contain only letters and spaces'
        });
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
        return res.status(400).json({
            status: 'fail',
            message: 'Please provide a valid email address'
        });
    }

    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/.test(password)) {
        return res.status(400).json({
            status: 'fail',
            message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        });
    }

    if (!/^\d{11}$/.test(phone)) {
        return res.status(400).json({
            status: 'fail',
            message: 'Phone number must be exactly 11 digits'
        });
    }

    next();
};

const validateLogin = (req, res, next) => {
    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({
            status: 'fail',
            message: 'Please provide email and password'
        });
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
        return res.status(400).json({
            status: 'fail',
            message: 'Please provide a valid email address'
        });
    }

    next();
};

module.exports = {
    protect,
    requireAdmin,
    protectAdmin,
    validateRegistration,
    validateLogin
};
