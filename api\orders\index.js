const { ensureConnection } = require("../../config/db");
const OrderController = require("../../controllers/orderController");
const { protect } = require("../../middleware/authMiddleware");

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    // Apply authentication middleware for all methods except OPTIONS
    await new Promise((resolve, reject) => {
      protect(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    if (req.method === 'POST') {
      // POST /api/orders
      return await OrderController.createOrder(req, res);
    } else if (req.method === 'GET') {
      // GET /api/orders
      return await OrderController.getAllOrders(req, res);
    } else {
      return res.status(405).json({
        status: "error",
        message: "Method not allowed"
      });
    }

  } catch (error) {
    console.error("Orders API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
