const userController = require('../controllers/userController');
const express = require('express');
const router = express.Router();
const { cacheMiddleware } = require('../config/redis');
const { protectAdmin } = require('../middleware/authMiddleware');

// Public routes
router.route('/').get(cacheMiddleware(300), userController.getUsers);
router.route('/:id').get(cacheMiddleware(300), userController.getUser);

// Admin-only routes (protection removed)
router.route('/:id').put(userController.updateUser);
router.route('/:id').delete(userController.deleteUser);

module.exports = router;