const { ensureConnection } = require("../../config/db");
const userController = require("../../controllers/userController");
const { protect, requireAdmin } = require("../../middleware/authMiddleware");

// Helper function to apply middleware
const applyMiddleware = (middleware, req, res) => {
  return new Promise((resolve, reject) => {
    middleware(req, res, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
};

// Helper function to apply admin protection
const applyAdminProtection = async (req, res) => {
  await applyMiddleware(protect, req, res);
  await applyMiddleware(requireAdmin, req, res);
};

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Parse the URL to get the path segments
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // For catch-all routes in Vercel, we need to find the segments after 'users'
    const usersIndex = pathSegments.findIndex(segment => segment === 'users');
    const userId = usersIndex >= 0 && pathSegments[usersIndex + 1] ? pathSegments[usersIndex + 1] : null;
    
    console.log('Users catch-all API - URL:', req.url);
    console.log('Users catch-all API - Path segments:', pathSegments);
    console.log('Users catch-all API - usersIndex:', usersIndex, 'userId:', userId);
    
    if (userId) {
      // Handle individual user operations
      req.params = { id: userId };
      
      if (req.method === 'GET') {
        // GET /api/users/:id
        return await userController.getUser(req, res);
      } else if (req.method === 'PUT') {
        // PUT /api/users/:id - Admin only
        try {
          await applyAdminProtection(req, res);
          return await userController.updateUser(req, res);
        } catch (authError) {
          console.error("Admin protection error:", authError);
          return res.status(401).json({
            status: "error",
            message: "Unauthorized access"
          });
        }
      } else if (req.method === 'DELETE') {
        // DELETE /api/users/:id - Admin only
        try {
          await applyAdminProtection(req, res);
          return await userController.deleteUser(req, res);
        } catch (authError) {
          console.error("Admin protection error:", authError);
          return res.status(401).json({
            status: "error",
            message: "Unauthorized access"
          });
        }
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed for individual user"
        });
      }
    } else {
      // Handle collection operations - redirect to index.js or handle here
      if (req.method === 'GET') {
        // GET /api/users
        return await userController.getUsers(req, res);
      } else {
        return res.status(405).json({
          status: "error",
          message: "Method not allowed"
        });
      }
    }
    
  } catch (error) {
    console.error("Users catch-all API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
