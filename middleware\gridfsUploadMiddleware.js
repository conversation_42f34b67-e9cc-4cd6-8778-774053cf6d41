const multer = require('multer');
const { GridFSBucket } = require('mongodb');
const mongoose = require('mongoose');
const path = require('path');
const crypto = require('crypto');
const { config } = require('dotenv');
config();

// Custom storage engine for GridFS
const storage = multer.memoryStorage();

// File filter to accept only images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload only images.'), false);
  }
};

// Create multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Custom middleware to handle GridFS uploads
const uploadToGridFS = async (req, res, next) => {
  try {
    if (!req.files) {
      return next();
    }

    // Check if MongoDB connection is ready
    console.log('MongoDB connection readyState:', mongoose.connection.readyState);
    if (mongoose.connection.readyState !== 1) {
      throw new Error(`Database connection is not ready. ReadyState: ${mongoose.connection.readyState}`);
    }

    const db = mongoose.connection.db;
    console.log('MongoDB db object:', !!db);
    if (!db) {
      throw new Error('Database connection must be open to store files');
    }

    const bucket = new GridFSBucket(db, { bucketName: 'uploads' });

    // Process main image
    if (req.files.image && req.files.image.length > 0) {
      const file = req.files.image[0];
      const filename = file.fieldname + '-' + Date.now() + '-' + crypto.randomBytes(16).toString('hex') + path.extname(file.originalname);

      const uploadStream = bucket.openUploadStream(filename, {
        metadata: {
          originalname: file.originalname,
          mimetype: file.mimetype,
          uploadDate: new Date()
        }
      });

      await new Promise((resolve, reject) => {
        uploadStream.end(file.buffer);
        uploadStream.on('finish', () => {
          req.files.image[0].filename = filename;
          resolve();
        });
        uploadStream.on('error', reject);
      });
    }

    // Process additional images
    if (req.files.additionalImages && req.files.additionalImages.length > 0) {
      for (let i = 0; i < req.files.additionalImages.length; i++) {
        const file = req.files.additionalImages[i];
        const filename = file.fieldname + '-' + Date.now() + '-' + crypto.randomBytes(16).toString('hex') + path.extname(file.originalname);

        const uploadStream = bucket.openUploadStream(filename, {
          metadata: {
            originalname: file.originalname,
            mimetype: file.mimetype,
            uploadDate: new Date()
          }
        });

        await new Promise((resolve, reject) => {
          uploadStream.end(file.buffer);
          uploadStream.on('finish', () => {
            req.files.additionalImages[i].filename = filename;
            resolve();
          });
          uploadStream.on('error', reject);
        });
      }
    }

    next();
  } catch (error) {
    console.error('GridFS upload error:', error);
    res.status(500).json({
      status: 'fail',
      message: 'Error uploading files'
    });
  }
};

// Middleware for handling product uploads (main image and additional images)
const uploadProductImages = [
  upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'additionalImages', maxCount: 5 }
  ]),
  uploadToGridFS
];

// Serverless-compatible single middleware function
const uploadProductImagesServerless = async (req, res, next) => {
  try {
    // First apply multer middleware
    const multerMiddleware = upload.fields([
      { name: 'image', maxCount: 1 },
      { name: 'additionalImages', maxCount: 5 }
    ]);

    await new Promise((resolve, reject) => {
      multerMiddleware(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Then apply GridFS upload
    await uploadToGridFS(req, res, next);
  } catch (error) {
    console.error('Upload middleware error:', error);
    next(error);
  }
};

module.exports = {
  uploadProductImages,
  uploadProductImagesServerless
};
