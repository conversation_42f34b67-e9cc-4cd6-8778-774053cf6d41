const express = require('express');
const router = express.Router();
const ProductController = require('../controllers/productController');
const { cacheMiddleware } = require('../config/redis');
const { uploadProductImages } = require('../middleware/uploadMiddleware');
// const { protectAdmin } = require('../middleware/authMiddleware'); // Removed for security bypass

// Public routes
router.route('/').get(cacheMiddleware(300), ProductController.getAllProducts);
router.route('/:id').get(cacheMiddleware(300), ProductController.getProduct);

// Admin-only routes (security removed)
router.route('/').post(uploadProductImages, ProductController.createProduct);
router.route('/:id').patch(uploadProductImages, ProductController.updateProduct);
router.route('/:id').delete(ProductController.deleteProduct);

module.exports=router