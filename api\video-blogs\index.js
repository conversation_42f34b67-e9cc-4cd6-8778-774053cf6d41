const { ensureConnection } = require("../../config/db");
const VideoBlogController = require("../../controllers/videoBlogController");
const { uploadVideoBlogFiles, handleUploadErrors } = require("../../middleware/videoBlogUploadMiddleware");
const { protect, requireAdmin } = require("../../middleware/authMiddleware");

// Helper function to apply middleware
const applyMiddleware = (middleware, req, res) => {
  return new Promise((resolve, reject) => {
    middleware(req, res, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
};

// Helper function to apply admin protection
const applyAdminProtection = async (req, res) => {
  await applyMiddleware(protect, req, res);
  await applyMiddleware(requireAdmin, req, res);
};



module.exports = async (req, res) => {
  try {
    // Ensure database connection
    await ensureConnection();
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Range');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    if (req.method === 'POST') {
      // POST /api/video-blogs - Create video blog - Admin only
      try {
        await applyAdminProtection(req, res);
        return await VideoBlogController.createVideoBlog(req, res);
      } catch (error) {
        return res.status(500).json({
          status: "error",
          message: "Upload failed"
        });
      }
    } else if (req.method === 'GET') {
      // GET /api/video-blogs
      return await VideoBlogController.getAllVideoBlogs(req, res);
    } else {
      return res.status(405).json({
        status: "error",
        message: "Method not allowed"
      });
    }
    
  } catch (error) {
    console.error("Video Blogs API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: error.message
    });
  }
};
