const Product = require("../models/Product");
const User = require("../models/User");
const { clearCache } = require("../config/redis");
const { sendNewProductNotification } = require("../utils/emailService");
const {
  deleteFileByFilename,
  findFileByFilename,
} = require("../config/gridfs");
const mongoose = require("mongoose");

exports.createProduct = async (req, res) => {
  try {
    const productData = { ...req.body };

    // Convert string 'true'/'false' values to boolean
    if (productData.stock === "true") productData.stock = true;
    if (productData.stock === "false") productData.stock = false;

    // Handle main image upload
    if (req.files && req.files.image && req.files.image.length > 0) {
      productData.image = req.files.image[0].filename;
    }

    // Handle additional images upload
    if (
      req.files &&
      req.files.additionalImages &&
      req.files.additionalImages.length > 0
    ) {
      productData.images = req.files.additionalImages.map(
        (file) => file.filename
      );
    }

    // Log the product data for debugging
    console.log("Creating product with data:", productData);

    // Ensure all required fields are present
    const requiredFields = [
      "name",
      "type",
      "price",
      "quantity",
      "description",
      "category",
      "stock",
      "image",
    ];
    const missingFields = requiredFields.filter((field) => !productData[field]);

    if (missingFields.length > 0) {
      return res.status(400).json({
        status: "fail",
        message: `Missing required fields: ${missingFields.join(", ")}`,
      });
    }

    const product = await Product.create(productData);

    // Invalidate product cache
    await clearCache("api:/api/products*");

    // Send email notification to all users
    try {
      // Get all users' email addresses
      const users = await User.find({}, "email");

      if (users && users.length > 0) {
        // Extract email addresses from user objects
        const userEmails = users.map((user) => user.email);

        // Send notification email
        await sendNewProductNotification(userEmails, product);
        console.log(`Product notification sent to ${userEmails.length} users`);
      } else {
        console.log("No users found to send product notification");
      }
    } catch (emailError) {
      // Log the error but don't fail the product creation
      console.error("Error sending product notification emails:", emailError);
    }

    res.status(201).json({
      status: "success",
      data: {
        product,
      },
    });
  } catch (err) {
    console.error("Create product error:", err);

    // Ensure we always return a string message
    let errorMessage = "Error creating product";

    if (err.message) {
      errorMessage = err.message;
    } else if (typeof err === "object") {
      try {
        errorMessage = JSON.stringify(err);
      } catch (e) {
        errorMessage = "Unknown error occurred";
      }
    }

    res.status(400).json({
      status: "fail",
      message: errorMessage,
    });
  }
};

exports.getAllProducts = async (req, res) => {
  try {
    const products = await Product.find();

    // Determine the base URL dynamically
    const protocol = req.headers['x-forwarded-proto'] || 'https';
    const host = req.headers.host || req.headers['x-forwarded-host'];
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `${protocol}://${host}`
      : process.env.DEPLOYED_URL || `http://localhost:${process.env.PORT || 5002}`;

    // Append base URL to each product's image attribute
    const updatedProducts = products.map((product) => {
      return {
        ...product._doc,
        image: `${baseUrl}/api/images/file/${product.image}`,
        images:
          product.images && product.images.length > 0
            ? product.images.map(
                (img) => `${baseUrl}/api/images/file/${img}`
              )
            : [],
      };
    });

    res.status(200).json({
      status: "success",
      results: updatedProducts.length,
      data: {
        products: updatedProducts,
      },
    });
  } catch (err) {
    console.error("Get all products error:", err);

    let errorMessage = "Error fetching products";

    if (err.message) {
      errorMessage = err.message;
    } else if (typeof err === "object") {
      try {
        errorMessage = JSON.stringify(err);
      } catch (e) {
        errorMessage = "Unknown error occurred";
      }
    }

    res.status(404).json({
      status: "fail",
      message: errorMessage,
    });
  }
};

exports.getProduct = async (req, res) => {
  try {
    const id = req.params.id;
    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({
        status: "fail",
        message: "Product not found",
      });
    }

    // Determine the base URL dynamically
    const protocol = req.headers['x-forwarded-proto'] || 'https';
    const host = req.headers.host || req.headers['x-forwarded-host'];
    const baseUrl = process.env.NODE_ENV === 'production'
      ? `${protocol}://${host}`
      : process.env.DEPLOYED_URL || `http://localhost:${process.env.PORT || 5002}`;

    // Create a response object with updated image URLs
    const productResponse = {
      ...product._doc,
      image: `${baseUrl}/api/images/file/${product.image}`,
      images:
        product.images && product.images.length > 0
          ? product.images.map(
              (img) => `${baseUrl}/api/images/file/${img}`
            )
          : [],
    };

    res.status(200).json({
      status: "success",
      data: productResponse,
    });
  } catch (err) {
    console.error("Get product error:", err);

    // Ensure we always return a string message
    let errorMessage = "Error fetching product";

    if (err.message) {
      errorMessage = err.message;
    } else if (typeof err === "object") {
      try {
        errorMessage = JSON.stringify(err);
      } catch (e) {
        errorMessage = "Unknown error occurred";
      }
    }

    res.status(404).json({
      status: "fail",
      message: errorMessage,
    });
  }
};

exports.updateProduct = async (req, res) => {
  try {
    const id = req.params.id;

    // Get the existing product to check for image replacement
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      return res.status(404).json({
        status: "fail",
        message: "Product not found",
      });
    }

    const productData = { ...req.body };

    // Convert string 'true'/'false' values to boolean
    if (productData.stock === "true") productData.stock = true;
    if (productData.stock === "false") productData.stock = false;

    // Handle main image upload and replacement
    if (req.files && req.files.image && req.files.image.length > 0) {
      // Delete the old image from GridFS if it exists
      if (existingProduct.image) {
        await deleteFileByFilename(existingProduct.image);
      }

      // Set the new image
      productData.image = req.files.image[0].filename;
    } else if (
      productData.keepExistingImage === "true" &&
      existingProduct.image
    ) {
      // If keepExistingImage flag is set and there's an existing image, use it
      productData.image = existingProduct.image;
    }

    // Handle additional images upload
    if (
      req.files &&
      req.files.additionalImages &&
      req.files.additionalImages.length > 0
    ) {
      // Get new additional images
      const newAdditionalImages = req.files.additionalImages.map(
        (file) => file.filename
      );

      // Combine with existing images if not replacing all
      if (
        !productData.replaceAllImages &&
        existingProduct.images &&
        existingProduct.images.length > 0
      ) {
        productData.images = [
          ...existingProduct.images,
          ...newAdditionalImages,
        ];
      } else {
        // Delete old additional images if replacing all
        if (existingProduct.images && existingProduct.images.length > 0) {
          for (const imageName of existingProduct.images) {
            await deleteFileByFilename(imageName);
          }
        }

        productData.images = newAdditionalImages;
      }
    } else if (existingProduct.images && existingProduct.images.length > 0) {
      // Keep existing additional images if no new ones are uploaded
      productData.images = existingProduct.images;
    }

    // Remove flags from the data to be saved
    delete productData.keepExistingImage;
    delete productData.replaceAllImages;

    console.log("Product data to be saved:", productData);

    // Update the product
    const product = await Product.findByIdAndUpdate(id, productData, {
      new: true,
      runValidators: true,
    });

    // Invalidate both the specific product cache and the all products cache
    await Promise.all([
      clearCache(`api:/api/products/${id}`),
      clearCache("api:/api/products*"),
    ]);

    res.status(200).json({
      status: "success",
      data: {
        product,
      },
    });
  } catch (err) {
    console.error("Error updating product:", err);

    // Ensure we always return a string message
    let errorMessage = "Error updating product";

    if (err.message) {
      errorMessage = err.message;
    } else if (typeof err === "object") {
      try {
        errorMessage = JSON.stringify(err);
      } catch (e) {
        errorMessage = "Unknown error occurred";
      }
    }

    res.status(404).json({
      status: "fail",
      message: errorMessage,
    });
  }
};

exports.deleteProduct = async (req, res) => {
  try {
    const id = req.params.id;
    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({
        status: "fail",
        message: "Product not found",
      });
    }

    // Delete main image from GridFS
    if (product.image) {
      await deleteFileByFilename(product.image);
    }

    // Delete additional images from GridFS
    if (product.images && product.images.length > 0) {
      for (const imageName of product.images) {
        await deleteFileByFilename(imageName);
      }
    }

    // Delete the product from the database
    await Product.findByIdAndDelete(id);

    // Invalidate both the specific product cache and the all products cache
    await Promise.all([
      clearCache(`api:/api/products/${id}`),
      clearCache("api:/api/products*"),
    ]);

    res.status(204).json({
      status: "success",
      data: null,
    });
  } catch (err) {
    console.error("Delete product error:", err);

    // Ensure we always return a string message
    let errorMessage = "Error deleting product";

    if (err.message) {
      errorMessage = err.message;
    } else if (typeof err === "object") {
      try {
        errorMessage = JSON.stringify(err);
      } catch (e) {
        errorMessage = "Unknown error occurred";
      }
    }

    res.status(404).json({
      status: "fail",
      message: errorMessage,
    });
  }
};
